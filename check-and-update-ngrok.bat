@echo off
echo 🔍 Vérification de l'URL ngrok actuelle...
echo.

REM Obtenir l'URL ngrok actuelle via l'API locale
curl -s http://localhost:4040/api/tunnels > temp_ngrok.json 2>nul

if %errorlevel% neq 0 (
    echo ❌ Impossible de se connecter à l'API ngrok locale
    echo 💡 Vérifiez que ngrok est bien démarré avec: start-ngrok.bat
    echo.
    pause
    exit /b 1
)

REM Extraire l'URL HTTPS du JSON (méthode simple pour Windows)
for /f "tokens=*" %%i in ('findstr "https.*ngrok-free.app" temp_ngrok.json') do (
    set "ngrok_line=%%i"
)

REM Nettoyer la ligne pour extraire juste l'URL
for /f "tokens=2 delims=:" %%a in ('echo %ngrok_line%') do (
    for /f "tokens=1 delims=," %%b in ("%%a") do (
        set "ngrok_url=%%b"
    )
)

REM Supprimer les guillemets et espaces
set "ngrok_url=%ngrok_url:"=%"
set "ngrok_url=%ngrok_url: =%"
set "ngrok_url=https:%ngrok_url%"

echo 🌐 URL ngrok actuelle: %ngrok_url%
echo.

REM Lire l'URL actuelle dans le fichier de configuration
for /f "tokens=*" %%i in ('findstr "baseUrl" frontend\lib\config\api_config.dart') do (
    set "current_config=%%i"
)

echo 📝 Configuration actuelle: %current_config%
echo.

REM Vérifier si l'URL a changé
echo %current_config% | findstr "%ngrok_url%" >nul
if %errorlevel% equ 0 (
    echo ✅ L'URL ngrok est déjà à jour dans la configuration
    echo.
    echo 🧪 Test de connectivité...
    curl -s -H "ngrok-skip-browser-warning: true" "%ngrok_url%/api/auth/login" -X POST -H "Content-Type: application/json" -d "{\"test\":\"connectivity\"}" >nul
    if %errorlevel% equ 0 (
        echo ✅ Le serveur répond correctement
    ) else (
        echo ❌ Le serveur ne répond pas. Vérifiez que le backend est démarré.
    )
) else (
    echo 🔄 Mise à jour nécessaire de l'URL ngrok...
    
    REM Créer une sauvegarde
    copy "frontend\lib\config\api_config.dart" "frontend\lib\config\api_config.dart.backup" >nul
    
    REM Mettre à jour le fichier de configuration
    powershell -Command "(Get-Content 'frontend\lib\config\api_config.dart') -replace 'https://[^'']*ngrok-free\.app', '%ngrok_url%' | Set-Content 'frontend\lib\config\api_config.dart'"
    
    echo ✅ Configuration mise à jour avec la nouvelle URL: %ngrok_url%
    echo.
    echo 🧪 Test de connectivité...
    curl -s -H "ngrok-skip-browser-warning: true" "%ngrok_url%/api/auth/login" -X POST -H "Content-Type: application/json" -d "{\"test\":\"connectivity\"}" >nul
    if %errorlevel% equ 0 (
        echo ✅ Le serveur répond correctement avec la nouvelle URL
    ) else (
        echo ❌ Le serveur ne répond toujours pas. Vérifiez que le backend est démarré.
    )
)

REM Nettoyer le fichier temporaire
del temp_ngrok.json 2>nul

echo.
echo 📱 Vous pouvez maintenant tester la connexion dans votre application Flutter
echo.
pause
