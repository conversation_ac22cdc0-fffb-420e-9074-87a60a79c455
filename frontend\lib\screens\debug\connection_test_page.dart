import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../config/api_config.dart';

class ConnectionTestPage extends StatefulWidget {
  const ConnectionTestPage({Key? key}) : super(key: key);

  @override
  State<ConnectionTestPage> createState() => _ConnectionTestPageState();
}

class _ConnectionTestPageState extends State<ConnectionTestPage> {
  List<String> testResults = [];
  bool isRunning = false;

  void addResult(String result) {
    setState(() {
      testResults.add("${DateTime.now().toString().substring(11, 19)}: $result");
    });
  }

  Future<void> runConnectionTests() async {
    setState(() {
      isRunning = true;
      testResults.clear();
    });

    addResult("🔍 Début des tests de connectivité...");

    // Test 1: Vérifier l'URL de base
    addResult("📡 Test 1: Vérification de l'URL de base");
    addResult("URL configurée: ${ApiConfig.baseUrl}");

    try {
      // Test simple de ping
      final response = await http.get(
        Uri.parse(ApiConfig.baseUrl),
        headers: {
          'ngrok-skip-browser-warning': 'true',
          'User-Agent': 'Flutter-Test-App',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      addResult("✅ Serveur accessible (Status: ${response.statusCode})");

      // Vérifier si c'est une page HTML d'erreur ngrok
      if (response.body.contains('<!DOCTYPE html>') && response.body.contains('ngrok')) {
        addResult("⚠️ Page d'avertissement ngrok détectée");
        addResult("💡 Solution: L'en-tête ngrok-skip-browser-warning doit être configuré");
      }
    } catch (e) {
      addResult("❌ Serveur inaccessible: $e");
      if (e.toString().contains('ClientException')) {
        addResult("💡 Solutions possibles:");
        addResult("   • Vérifiez que le serveur backend est démarré");
        addResult("   • Vérifiez que ngrok est actif");
        addResult("   • L'URL ngrok a peut-être changé");
        addResult("   • Utilisez le script update-ngrok-url.ps1");
      }
    }

    // Test 2: Test de l'endpoint de login
    addResult("\n📡 Test 2: Test de l'endpoint de login");
    addResult("URL de login: ${ApiConfig.loginUrl}");
    
    try {
      final response = await http.post(
        Uri.parse(ApiConfig.loginUrl),
        headers: {
          'Content-Type': 'application/json',
          'ngrok-skip-browser-warning': 'true',
          'User-Agent': 'Flutter-Test-App',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'email': '<EMAIL>',
          'password': 'test123'
        }),
      ).timeout(const Duration(seconds: 15));
      
      addResult("✅ Endpoint de login accessible (Status: ${response.statusCode})");
      addResult("Réponse: ${response.body.substring(0, response.body.length > 100 ? 100 : response.body.length)}...");
    } catch (e) {
      addResult("❌ Endpoint de login inaccessible: $e");
    }

    // Test 3: Test avec différents headers
    addResult("\n📡 Test 3: Test avec headers alternatifs");
    
    try {
      final response = await http.post(
        Uri.parse(ApiConfig.loginUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': '*/*',
          'User-Agent': 'Mozilla/5.0 (compatible; Flutter)',
        },
        body: jsonEncode({
          'email': '<EMAIL>',
          'password': 'test123'
        }),
      ).timeout(const Duration(seconds: 15));
      
      addResult("✅ Test avec headers alternatifs réussi (Status: ${response.statusCode})");
    } catch (e) {
      addResult("❌ Test avec headers alternatifs échoué: $e");
    }

    // Test 4: Vérification DNS
    addResult("\n📡 Test 4: Informations de diagnostic");
    addResult("URL complète de login: ${ApiConfig.loginUrl}");
    addResult("Headers par défaut: ${ApiConfig.defaultHeaders}");
    
    addResult("\n🔧 Suggestions de dépannage:");
    addResult("1. Vérifiez que le serveur backend est démarré");
    addResult("2. Vérifiez que ngrok est actif et l'URL est correcte");
    addResult("3. Testez l'URL dans un navigateur web");
    addResult("4. Vérifiez votre connexion internet");

    setState(() {
      isRunning = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test de Connectivité'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Configuration Actuelle',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('URL de base: ${ApiConfig.baseUrl}'),
                    Text('URL de login: ${ApiConfig.loginUrl}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: isRunning ? null : runConnectionTests,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 50),
              ),
              child: isRunning
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 10),
                        Text('Tests en cours...'),
                      ],
                    )
                  : const Text('Lancer les Tests de Connectivité'),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Résultats des Tests',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: testResults.isEmpty
                            ? const Center(
                                child: Text(
                                  'Cliquez sur le bouton pour lancer les tests',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              )
                            : ListView.builder(
                                itemCount: testResults.length,
                                itemBuilder: (context, index) {
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 2.0),
                                    child: Text(
                                      testResults[index],
                                      style: const TextStyle(
                                        fontFamily: 'monospace',
                                        fontSize: 12,
                                      ),
                                    ),
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
