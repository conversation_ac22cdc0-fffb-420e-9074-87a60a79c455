@echo off
echo 🧪 Test rapide de connectivité...
echo.

REM Vérifier si le backend est en cours d'exécution
echo 🔍 Vérification du serveur backend (port 5000)...
netstat -an | findstr :5000 | findstr LISTENING >nul
if %errorlevel% equ 0 (
    echo ✅ Serveur backend en cours d'exécution sur le port 5000
) else (
    echo ❌ Serveur backend non détecté sur le port 5000
    echo 💡 Démarrez le backend avec: start-backend.bat
    pause
    exit /b 1
)

REM Vérifier si ngrok est en cours d'exécution
echo 🔍 Vérification de ngrok...
tasklist | findstr ngrok.exe >nul
if %errorlevel% equ 0 (
    echo ✅ ngrok est en cours d'exécution
) else (
    echo ❌ ngrok n'est pas en cours d'exécution
    echo 💡 Démarrez ngrok avec: start-ngrok.bat
    pause
    exit /b 1
)

REM Tester la connectivité locale
echo 🔍 Test de connectivité locale...
curl -s http://localhost:5000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Serveur local accessible
) else (
    echo ❌ Serveur local non accessible
)

REM Obtenir et tester l'URL ngrok
echo 🔍 Récupération de l'URL ngrok...
curl -s http://localhost:4040/api/tunnels > temp_ngrok.json 2>nul
if %errorlevel% neq 0 (
    echo ❌ Impossible d'accéder à l'API ngrok
    echo 💡 Vérifiez que ngrok est bien démarré
    pause
    exit /b 1
)

REM Extraire l'URL HTTPS (méthode simplifiée)
for /f "tokens=*" %%i in ('findstr "https.*ngrok-free.app" temp_ngrok.json') do (
    set "ngrok_line=%%i"
    goto :found_url
)

:found_url
if defined ngrok_line (
    REM Extraire l'URL de la ligne JSON
    for /f "tokens=2 delims=:" %%a in ('echo %ngrok_line%') do (
        for /f "tokens=1 delims=," %%b in ("%%a") do (
            set "ngrok_url=%%b"
        )
    )
    
    REM Nettoyer l'URL
    set "ngrok_url=%ngrok_url:"=%"
    set "ngrok_url=%ngrok_url: =%"
    set "ngrok_url=https:%ngrok_url%"
    
    echo 🌐 URL ngrok trouvée: %ngrok_url%
    
    REM Tester la connectivité ngrok
    echo 🔍 Test de connectivité ngrok...
    curl -s -H "ngrok-skip-browser-warning: true" "%ngrok_url%" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ URL ngrok accessible
        
        REM Tester l'endpoint de login
        echo 🔍 Test de l'endpoint de login...
        curl -s -H "ngrok-skip-browser-warning: true" -H "Content-Type: application/json" "%ngrok_url%/api/auth/login" -X POST -d "{\"test\":\"connectivity\"}" >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✅ Endpoint de login accessible
        ) else (
            echo ⚠️  Endpoint de login non accessible (peut être normal)
        )
    ) else (
        echo ❌ URL ngrok non accessible
        echo 💡 L'URL dans votre configuration Flutter doit être mise à jour
    )
) else (
    echo ❌ Impossible de trouver l'URL ngrok
)

REM Nettoyer
del temp_ngrok.json 2>nul

echo.
echo 🔧 Pour corriger les problèmes:
echo   1. Assurez-vous que le backend est démarré: start-backend.bat
echo   2. Assurez-vous que ngrok est démarré: start-ngrok.bat  
echo   3. Mettez à jour l'URL ngrok: .\update-ngrok-url.ps1
echo.
pause
