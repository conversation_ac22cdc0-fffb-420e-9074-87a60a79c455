@echo off
title Démarrage complet de l'environnement Alo Wenk
echo ========================================
echo 🚀 DÉMARRAGE COMPLET DE L'ENVIRONNEMENT
echo ========================================
echo.

REM Vérifier si le backend est déjà en cours d'exécution
echo 🔍 Vérification du serveur backend...
netstat -an | findstr :5000 | findstr LISTENING >nul
if %errorlevel% equ 0 (
    echo ✅ Serveur backend déjà en cours d'exécution
) else (
    echo 🚀 Démarrage du serveur backend...
    start "Backend Server" cmd /k "cd backend && npm install && npm start"
    echo ⏳ Attente du démarrage du serveur...
    timeout /t 10 /nobreak >nul
)

REM Vérifier si ngrok est déjà en cours d'exécution
echo 🔍 Vérification de ngrok...
tasklist | findstr ngrok.exe >nul
if %errorlevel% equ 0 (
    echo ✅ ngrok déjà en cours d'exécution
) else (
    echo 🌐 Démarrage de ngrok...
    start "Ngrok Tunnel" cmd /k "ngrok http 5000"
    echo ⏳ Attente du démarrage de ngrok...
    timeout /t 15 /nobreak >nul
)

REM Attendre que les services soient prêts
echo ⏳ Vérification que tous les services sont prêts...
timeout /t 5 /nobreak >nul

REM Vérifier la connectivité
echo 🧪 Test de connectivité...
call test-connection-quick.bat

REM Mettre à jour automatiquement l'URL ngrok
echo 🔄 Mise à jour automatique de l'URL ngrok...
powershell -ExecutionPolicy Bypass -File "update-ngrok-url.ps1"

echo.
echo ========================================
echo ✅ ENVIRONNEMENT PRÊT
echo ========================================
echo.
echo 📱 Vous pouvez maintenant:
echo   1. Lancer votre application Flutter
echo   2. Tester la connexion avec les identifiants
echo   3. Utiliser l'application normalement
echo.
echo 🔧 Scripts utiles:
echo   - test-connection-quick.bat : Test rapide de connectivité
echo   - update-ngrok-url.ps1 : Mise à jour de l'URL ngrok
echo   - check-and-update-ngrok.bat : Vérification complète
echo.
pause
