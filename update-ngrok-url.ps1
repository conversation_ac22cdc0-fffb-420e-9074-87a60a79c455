# Script PowerShell pour mettre à jour automatiquement l'URL ngrok
Write-Host "🔍 Vérification de l'URL ngrok actuelle..." -ForegroundColor Cyan

try {
    # Obtenir l'URL ngrok actuelle via l'API locale
    $response = Invoke-RestMethod -Uri "http://localhost:4040/api/tunnels" -Method Get
    $httpsUrl = $response.tunnels | Where-Object { $_.proto -eq "https" } | Select-Object -First 1 -ExpandProperty public_url

    if (-not $httpsUrl) {
        Write-Host "❌ Aucun tunnel HTTPS trouvé dans ngrok" -ForegroundColor Red
        Write-Host "💡 Vérifiez que ngrok est bien démarré avec: .\start-ngrok.bat" -ForegroundColor Yellow
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }

    Write-Host "🌐 URL ngrok actuelle: $httpsUrl" -ForegroundColor Green

    # Lire le fichier de configuration actuel
    $configFile = "frontend\lib\config\api_config.dart"
    $configContent = Get-Content $configFile -Raw

    # Extraire l'URL actuelle
    if ($configContent -match "baseUrl = '(https://[^']+)'") {
        $currentUrl = $matches[1]
        Write-Host "📝 URL actuelle dans la config: $currentUrl" -ForegroundColor Yellow

        if ($currentUrl -eq $httpsUrl) {
            Write-Host "✅ L'URL ngrok est déjà à jour dans la configuration" -ForegroundColor Green
        } else {
            Write-Host "🔄 Mise à jour nécessaire..." -ForegroundColor Yellow

            # Créer une sauvegarde
            Copy-Item $configFile "$configFile.backup" -Force
            Write-Host "💾 Sauvegarde créée: $configFile.backup" -ForegroundColor Gray

            # Mettre à jour l'URL
            $newContent = $configContent -replace "baseUrl = 'https://[^']+ngrok-free\.app'", "baseUrl = '$httpsUrl'"
            Set-Content $configFile $newContent -Encoding UTF8

            Write-Host "✅ Configuration mise à jour avec: $httpsUrl" -ForegroundColor Green
        }
    } else {
        Write-Host "❌ Impossible de trouver l'URL baseUrl dans le fichier de configuration" -ForegroundColor Red
        exit 1
    }

    # Test de connectivité
    Write-Host "🧪 Test de connectivité au serveur..." -ForegroundColor Cyan

    $headers = @{
        "Content-Type" = "application/json"
        "ngrok-skip-browser-warning" = "true"
        "Accept" = "application/json"
    }

    $testData = @{
        "test" = "connectivity"
    } | ConvertTo-Json

    try {
        $testResponse = Invoke-RestMethod -Uri "$httpsUrl/api/auth/login" -Method Post -Headers $headers -Body $testData -TimeoutSec 10
        Write-Host "✅ Le serveur répond correctement" -ForegroundColor Green
    } catch {
        if ($_.Exception.Message -like "*401*" -or $_.Exception.Message -like "*400*") {
            Write-Host "✅ Le serveur répond (erreur attendue pour test de connectivité)" -ForegroundColor Green
        } else {
            Write-Host "❌ Le serveur ne répond pas: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "💡 Vérifiez que le backend est démarré avec: .\start-backend.bat" -ForegroundColor Yellow
        }
    }

} catch {
    Write-Host "❌ Erreur lors de la connexion à l'API ngrok: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Vérifiez que ngrok est bien démarré" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📱 Vous pouvez maintenant tester la connexion dans votre application Flutter" -ForegroundColor Cyan
Read-Host "Appuyez sur Entrée pour continuer"
Write-Host ""
