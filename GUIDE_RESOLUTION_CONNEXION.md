# 🔧 Guide de Résolution des Problèmes de Connexion

## ❌ Erreur: "ClientException: Failed to fetch"

Cette erreur indique que l'application Flutter ne peut pas se connecter au serveur backend via ngrok.

### 🔍 Diagnostic Rapide

1. **Exécutez le script de diagnostic:**
   ```bash
   test-connection-quick.bat
   ```

2. **Ou utilisez le script complet:**
   ```bash
   start-complete-environment.bat
   ```

### 🛠️ Solutions par Étapes

#### Étape 1: Vérifier le Backend
```bash
# Vérifier si le serveur est en cours d'exécution
netstat -an | findstr :5000
```

**Si aucun résultat:** Démarrez le backend
```bash
start-backend.bat
```

#### Étape 2: Vérifier ngrok
```bash
# Vérifier si ngrok est en cours d'exécution
tasklist | findstr ngrok
```

**Si aucun résultat:** <PERSON><PERSON><PERSON><PERSON> ngrok
```bash
start-ngrok.bat
```

#### Étape 3: Mettre à jour l'URL ngrok
L'URL ngrok change à chaque redémarrage. Mettez-la à jour automatiquement:
```bash
powershell -ExecutionPolicy Bypass -File update-ngrok-url.ps1
```

#### Étape 4: Tester la Connectivité
Dans votre application Flutter:
1. Allez sur la page de connexion
2. Cliquez sur "🔧 Diagnostic de connexion"
3. Lancez les tests

### 🚀 Solution Automatique Complète

Utilisez ce script pour tout démarrer automatiquement:
```bash
start-complete-environment.bat
```

Ce script:
- ✅ Démarre le backend si nécessaire
- ✅ Démarre ngrok si nécessaire  
- ✅ Met à jour automatiquement l'URL ngrok
- ✅ Teste la connectivité
- ✅ Affiche un rapport complet

### 🔧 Dépannage Avancé

#### Problème: ngrok ne démarre pas
```bash
# Vérifier si le port 5000 est libre
netstat -an | findstr :5000

# Si occupé par un autre processus, l'arrêter ou changer de port
```

#### Problème: Backend ne démarre pas
```bash
cd backend
npm install
npm start
```

#### Problème: URL ngrok incorrecte
1. Ouvrez http://localhost:4040 dans votre navigateur
2. Copiez l'URL HTTPS affichée
3. Mettez à jour manuellement dans `frontend/lib/config/api_config.dart`

### 📱 Test dans Flutter

Après avoir résolu les problèmes:
1. Redémarrez votre application Flutter
2. Testez la connexion avec:
   - Email: `<EMAIL>`
   - Mot de passe: `12345678`

### 🆘 Si Rien ne Fonctionne

1. **Redémarrez tout:**
   ```bash
   # Arrêter tous les processus
   taskkill /f /im ngrok.exe
   taskkill /f /im node.exe
   
   # Redémarrer complètement
   start-complete-environment.bat
   ```

2. **Vérifiez les logs:**
   - Logs du backend dans la console
   - Logs de ngrok dans sa console
   - Logs Flutter dans votre IDE

3. **Contactez le support** avec:
   - Capture d'écran des erreurs
   - Résultat de `test-connection-quick.bat`
   - Logs des consoles

### 📋 Checklist de Vérification

- [ ] Backend démarré (port 5000)
- [ ] ngrok démarré et accessible
- [ ] URL ngrok mise à jour dans la config
- [ ] Test de connectivité réussi
- [ ] Application Flutter redémarrée
- [ ] Test de connexion effectué

---

**💡 Conseil:** Utilisez toujours `start-complete-environment.bat` pour démarrer votre environnement de développement. Il automatise toutes ces vérifications.
