// lib/services/api_service.dart
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../config/api_config.dart';

class ApiService {
  // Méthode pour faire un appel GET avec timeout et gestion d'erreur
  static Future<ApiResponse> get(String url, {String? token}) async {
    try {
      print("Requête GET vers: $url");

      final headers = Map<String, String>.from(ApiConfig.defaultHeaders);
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      // Headers spécifiques pour ngrok
      headers['ngrok-skip-browser-warning'] = 'true';
      headers['User-Agent'] = 'Flutter App';
      headers['Accept'] = 'application/json';

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      ).timeout(const Duration(seconds: 30)); // Augmenter le timeout

      print("Statut de la réponse: ${response.statusCode}");

      // Vérifier si c'est une page HTML d'erreur ngrok
      if (response.body.contains('<!DOCTYPE html>') && response.body.contains('ngrok')) {
        print("Réponse HTML détectée pour ${Uri.parse(url).path}: ${response.body.substring(0, 200)}...");
        return ApiResponse(
          statusCode: 502,
          body: '{"error": "Page d\'avertissement ngrok détectée. Vérifiez que l\'en-tête ngrok-skip-browser-warning est envoyé."}',
          isSuccess: false,
          error: 'Page d\'avertissement ngrok',
        );
      }

      print("Corps de la réponse: ${response.body}");

      return ApiResponse(
        statusCode: response.statusCode,
        body: response.body,
        isSuccess: response.statusCode >= 200 && response.statusCode < 300,
      );
    } catch (e) {
      print("Exception lors de l'appel API GET: $e");
      return ApiResponse(
        statusCode: 0,
        body: '',
        isSuccess: false,
        error: e.toString(),
      );
    }
  }

  // Méthode pour faire un appel POST avec timeout et gestion d'erreur
  static Future<ApiResponse> post(String url, Map<String, dynamic> data, {String? token}) async {
    try {
      print("Envoi de données POST vers: $url");
      print("Données: ${jsonEncode(data)}");

      final headers = Map<String, String>.from(ApiConfig.defaultHeaders);
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      // Headers spécifiques pour ngrok - simplifiés et optimisés
      headers['ngrok-skip-browser-warning'] = 'true';
      headers['User-Agent'] = 'Flutter-App/1.0';
      headers['Accept'] = 'application/json';

      // Tentative de connexion avec retry automatique
      http.Response? response;
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          print("Tentative ${retryCount + 1}/$maxRetries...");

          response = await http.post(
            Uri.parse(url),
            headers: headers,
            body: jsonEncode(data),
          ).timeout(
            const Duration(seconds: 30), // Timeout réduit mais avec retry
            onTimeout: () {
              throw Exception('Timeout: Le serveur met trop de temps à répondre');
            },
          );

          // Si on arrive ici, la requête a réussi
          break;

        } catch (e) {
          retryCount++;
          if (retryCount >= maxRetries) {
            rethrow; // Relancer l'exception après tous les essais
          }

          print("Échec tentative $retryCount: $e");
          print("Nouvelle tentative dans 2 secondes...");
          await Future.delayed(const Duration(seconds: 2));
        }
      }

      if (response == null) {
        throw Exception('Impossible d\'obtenir une réponse du serveur après $maxRetries tentatives');
      }

      print("Statut de la réponse: ${response.statusCode}");
      print("Corps de la réponse: ${response.body}");

      return ApiResponse(
        statusCode: response.statusCode,
        body: response.body,
        isSuccess: response.statusCode >= 200 && response.statusCode < 300,
      );
    } on http.ClientException catch (e) {
      print("ClientException lors de l'appel API POST: $e");
      String detailedError = 'Erreur de connexion au serveur.\n\n';

      if (e.message.contains('Failed to fetch')) {
        detailedError += 'Solutions possibles:\n'
            '• Vérifiez que le serveur backend est démarré (port 5000)\n'
            '• Vérifiez que ngrok est actif et accessible\n'
            '• L\'URL ngrok a peut-être changé - utilisez le script de mise à jour\n'
            '• Vérifiez votre connexion internet\n\n'
            'Détails techniques: ${e.message}';
      } else {
        detailedError += 'Détails: ${e.message}';
      }

      return ApiResponse(
        statusCode: 0,
        body: '',
        isSuccess: false,
        error: detailedError,
      );
    } on Exception catch (e) {
      print("Exception lors de l'appel API POST: $e");
      return ApiResponse(
        statusCode: 0,
        body: '',
        isSuccess: false,
        error: e.toString(),
      );
    }
  }

  // Méthode pour faire un appel PUT avec timeout et gestion d'erreur
  static Future<ApiResponse> put(String url, Map<String, dynamic> data, {String? token}) async {
    try {
      print("Envoi de données PUT vers: $url");
      print("Données: ${jsonEncode(data)}");

      final headers = Map<String, String>.from(ApiConfig.defaultHeaders);
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      // Headers spécifiques pour ngrok
      headers['ngrok-skip-browser-warning'] = 'true';
      headers['User-Agent'] = 'Flutter App';
      headers['Accept'] = 'application/json';

      final response = await http.put(
        Uri.parse(url),
        headers: headers,
        body: jsonEncode(data),
      ).timeout(const Duration(seconds: 30)); // Augmenter le timeout
      
      print("Statut de la réponse: ${response.statusCode}");
      print("Corps de la réponse: ${response.body}");
      
      return ApiResponse(
        statusCode: response.statusCode,
        body: response.body,
        isSuccess: response.statusCode >= 200 && response.statusCode < 300,
      );
    } catch (e) {
      print("Exception lors de l'appel API PUT: $e");
      return ApiResponse(
        statusCode: 0,
        body: '',
        isSuccess: false,
        error: e.toString(),
      );
    }
  }

  // Méthode pour les appels DELETE avec timeout et gestion d'erreur
  static Future<ApiResponse> delete(String url, {String? token}) async {
    try {
      print("Requête DELETE vers: $url");

      final headers = Map<String, String>.from(ApiConfig.defaultHeaders);
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      // Headers spécifiques pour ngrok
      headers['ngrok-skip-browser-warning'] = 'true';
      headers['User-Agent'] = 'Flutter App';
      headers['Accept'] = 'application/json';

      final response = await http.delete(
        Uri.parse(url),
        headers: headers,
      ).timeout(const Duration(seconds: 30)); // Augmenter le timeout
      
      print("Statut de la réponse: ${response.statusCode}");
      print("Corps de la réponse: ${response.body}");
      
      return ApiResponse(
        statusCode: response.statusCode,
        body: response.body,
        isSuccess: response.statusCode >= 200 && response.statusCode < 300,
      );
    } catch (e) {
      print("Exception lors de l'appel API DELETE: $e");
      return ApiResponse(
        statusCode: 0,
        body: '',
        isSuccess: false,
        error: e.toString(),
      );
    }
  }

  // Méthode spécifique pour la connexion
  static Future<LoginResult> login(String email, String password) async {
    try {
      print("🔐 Tentative de connexion pour: $email");

      final response = await post(ApiConfig.loginUrl, {
        'email': email,
        'password': password,
      });

      if (response.isSuccess) {
        try {
          final data = jsonDecode(response.body);
          final user = data['user'];
          print("✅ Connexion réussie pour l'utilisateur: ${user['id']}");
          return LoginResult(
            success: true,
            userId: user['id'],
            role: user['role'],
            token: data['token'],
          );
        } catch (e) {
          print("❌ Erreur de parsing de la réponse: $e");
          return LoginResult(
            success: false,
            errorMessage: 'Erreur de format de réponse du serveur.',
          );
        }
      } else {
        String errorMessage;
        try {
          final data = jsonDecode(response.body);
          errorMessage = data['message'] ?? ApiConfig.getErrorMessage(response.statusCode);
        } catch (e) {
          if (response.statusCode == 0) {
            // Erreur de connexion réseau
            if (response.error?.contains('ClientException') == true) {
              errorMessage = 'Impossible de se connecter au serveur.\n\n'
                  'Solutions possibles:\n'
                  '• Vérifiez que le serveur backend est démarré\n'
                  '• Vérifiez que ngrok est actif\n'
                  '• Testez la connectivité avec le bouton de diagnostic\n'
                  '• Vérifiez votre connexion internet';
            } else {
              errorMessage = ApiConfig.getConnectionErrorMessage(response.error);
            }
          } else {
            errorMessage = ApiConfig.getErrorMessage(response.statusCode);
          }
        }

        print("❌ Échec de connexion: $errorMessage");
        return LoginResult(
          success: false,
          errorMessage: errorMessage,
        );
      }
    } catch (e) {
      print("❌ Exception lors de la connexion: $e");
      return LoginResult(
        success: false,
        errorMessage: 'Erreur de connexion au serveur. Vérifiez votre connexion internet.',
      );
    }
  }

  // Méthode spécifique pour l'inscription
  static Future<RegisterResult> register(Map<String, dynamic> userData) async {
    final response = await post(ApiConfig.registerUrl, userData);
    
    if (response.isSuccess) {
      try {
        final data = jsonDecode(response.body);
        return RegisterResult(
          success: true,
          userId: data['userId'].toString(),
        );
      } catch (e) {
        return RegisterResult(
          success: false,
          errorMessage: 'Erreur de format de réponse du serveur.',
        );
      }
    } else {
      String errorMessage;
      try {
        final data = jsonDecode(response.body);
        errorMessage = data['message'] ?? ApiConfig.getErrorMessage(response.statusCode);
      } catch (e) {
        if (response.statusCode == 0) {
          errorMessage = ApiConfig.getConnectionErrorMessage(response.error);
        } else {
          errorMessage = ApiConfig.getErrorMessage(response.statusCode);
        }
      }
      
      return RegisterResult(
        success: false,
        errorMessage: errorMessage,
      );
    }
  }
}

// Classes pour les réponses
class ApiResponse {
  final int statusCode;
  final String body;
  final bool isSuccess;
  final String? error;
  
  ApiResponse({
    required this.statusCode,
    required this.body,
    required this.isSuccess,
    this.error,
  });
}

class LoginResult {
  final bool success;
  final String? userId;
  final String? role;
  final String? token;
  final String? errorMessage;
  
  LoginResult({
    required this.success,
    this.userId,
    this.role,
    this.token,
    this.errorMessage,
  });
}

class RegisterResult {
  final bool success;
  final String? userId;
  final String? errorMessage;
  
  RegisterResult({
    required this.success,
    this.userId,
    this.errorMessage,
  });
}